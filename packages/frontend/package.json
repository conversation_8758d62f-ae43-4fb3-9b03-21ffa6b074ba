{"name": "frontend", "version": "1.0.0", "description": "Frontend for task management application", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"@mantine/core": "^7.3.0", "@mantine/hooks": "^7.3.0", "@mantine/notifications": "^7.3.0", "@mantine/modals": "^7.3.0", "@tabler/icons-react": "^2.44.0", "@tanstack/react-query": "^5.8.0", "@trpc/client": "^10.45.0", "@trpc/react-query": "^10.45.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^23.0.1", "postcss": "^8.4.31", "postcss-preset-mantine": "^1.8.0", "postcss-simple-vars": "^7.0.1", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^1.0.0"}}