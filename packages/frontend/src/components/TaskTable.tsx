import { useState } from 'react';
import {
  Table,
  Card,
  Title,
  Button,
  Group,
  Badge,
  Text,
  ActionIcon,
  Select,
  TextInput,
  Modal,
  Stack,
  LoadingOverlay,
} from '@mantine/core';
import { IconPlus, IconEdit, IconTrash } from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { trpc } from '../utils/trpc';
import { InlineEditCell } from './InlineEditCell';
import type { Task, NewTask, FieldDefinition, EditingCell } from '../types';

export function TaskTable() {
  const [editingCell, setEditingCell] = useState<EditingCell>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [newTask, setNewTask] = useState<NewTask>({
    name: '',
    description: '',
    status: 'Open',
    customFields: {},
  });

  const { data: tasks = [], refetch: refetchTasks, isLoading } = trpc.tasks.getAll.useQuery();
  const { data: fields = [] } = trpc.fields.getAll.useQuery();

  const createTask = trpc.tasks.create.useMutation({
    onSuccess: () => {
      notifications.show({
        title: 'Success',
        message: 'Task created successfully',
        color: 'green',
      });
      refetchTasks();
      setIsCreating(false);
      setNewTask({
        name: '',
        description: '',
        status: 'Open',
        customFields: {},
      });
    },
    onError: (error) => {
      notifications.show({
        title: 'Error',
        message: error.message,
        color: 'red',
      });
    },
  });

  const deleteTask = trpc.tasks.delete.useMutation({
    onSuccess: () => {
      notifications.show({
        title: 'Success',
        message: 'Task deleted successfully',
        color: 'green',
      });
      refetchTasks();
    },
    onError: (error) => {
      notifications.show({
        title: 'Error',
        message: error.message,
        color: 'red',
      });
    },
  });

  const handleCellDoubleClick = (
    taskId: string,
    fieldName: string,
    fieldType: 'string' | 'number' | 'boolean',
    currentValue: any
  ) => {
    setEditingCell({
      taskId,
      fieldName,
      fieldType,
      currentValue,
    });
  };

  const handleCreateTask = () => {
    if (!newTask.name.trim()) {
      notifications.show({
        title: 'Validation Error',
        message: 'Task name is required',
        color: 'red',
      });
      return;
    }

    // Initialize custom fields with default values
    const customFields: Record<string, any> = {};
    fields.forEach((field) => {
      if (field.defaultValue) {
        switch (field.type) {
          case 'number':
            customFields[field.name] = parseFloat(field.defaultValue) || 0;
            break;
          case 'boolean':
            customFields[field.name] = field.defaultValue === 'true';
            break;
          default:
            customFields[field.name] = field.defaultValue;
        }
      }
    });

    createTask.mutate({
      ...newTask,
      customFields,
    });
  };

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'Open':
        return 'blue';
      case 'In Progress':
        return 'yellow';
      case 'In Review':
        return 'orange';
      case 'Closed':
        return 'green';
      default:
        return 'gray';
    }
  };

  const renderCellValue = (task: Task, field: FieldDefinition | { name: string; type: string }) => {
    const value = field.name === 'name' ? task.name :
                  field.name === 'description' ? task.description :
                  field.name === 'status' ? task.status :
                  task.customFields?.[field.name];

    if (field.name === 'status') {
      return <Badge color={getStatusColor(value)}>{value}</Badge>;
    }

    if (field.type === 'boolean') {
      return <Badge color={value ? 'green' : 'red'}>{value ? 'Yes' : 'No'}</Badge>;
    }

    return <Text>{value || '-'}</Text>;
  };

  const allColumns = [
    { name: 'name', label: 'Name', type: 'string' },
    { name: 'description', label: 'Description', type: 'string' },
    { name: 'status', label: 'Status', type: 'string' },
    ...fields.map(f => ({ name: f.name, label: f.label, type: f.type })),
  ];

  return (
    <Card withBorder>
      <Group justify="space-between" mb="md">
        <Title order={3}>Tasks</Title>
        <Button
          leftSection={<IconPlus size={16} />}
          onClick={() => setIsCreating(true)}
        >
          Add Task
        </Button>
      </Group>

      <div style={{ position: 'relative' }}>
        <LoadingOverlay visible={isLoading} />
        
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              {allColumns.map((column) => (
                <Table.Th key={column.name}>{column.label}</Table.Th>
              ))}
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {tasks.map((task) => (
              <Table.Tr key={task.id}>
                {allColumns.map((column) => (
                  <Table.Td
                    key={`${task.id}-${column.name}`}
                    style={{ cursor: 'pointer' }}
                    onDoubleClick={() =>
                      handleCellDoubleClick(
                        task.id,
                        column.name,
                        column.type as any,
                        column.name === 'name' ? task.name :
                        column.name === 'description' ? task.description :
                        column.name === 'status' ? task.status :
                        task.customFields?.[column.name]
                      )
                    }
                  >
                    {renderCellValue(task, column)}
                  </Table.Td>
                ))}
                <Table.Td>
                  <ActionIcon
                    color="red"
                    variant="light"
                    onClick={() => deleteTask.mutate({ id: task.id })}
                  >
                    <IconTrash size={16} />
                  </ActionIcon>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>

        {tasks.length === 0 && !isLoading && (
          <Text ta="center" c="dimmed" py="xl">
            No tasks found. Create your first task!
          </Text>
        )}
      </div>

      {/* Inline Edit Modal */}
      {editingCell && (
        <InlineEditCell
          taskId={editingCell.taskId}
          fieldName={editingCell.fieldName}
          fieldType={editingCell.fieldType}
          currentValue={editingCell.currentValue}
          onClose={() => setEditingCell(null)}
          onSave={() => refetchTasks()}
        />
      )}

      {/* Create Task Modal */}
      <Modal
        opened={isCreating}
        onClose={() => setIsCreating(false)}
        title="Create New Task"
        size="md"
      >
        <Stack>
          <TextInput
            label="Task Name"
            placeholder="Enter task name"
            value={newTask.name}
            onChange={(e) => setNewTask({ ...newTask, name: e.target.value })}
            required
          />
          
          <TextInput
            label="Description"
            placeholder="Enter task description"
            value={newTask.description}
            onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
          />
          
          <Select
            label="Status"
            value={newTask.status}
            onChange={(value) => setNewTask({ ...newTask, status: value as any })}
            data={[
              { value: 'Open', label: 'Open' },
              { value: 'In Progress', label: 'In Progress' },
              { value: 'In Review', label: 'In Review' },
              { value: 'Closed', label: 'Closed' },
            ]}
          />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setIsCreating(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTask} loading={createTask.isPending}>
              Create Task
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Card>
  );
}
