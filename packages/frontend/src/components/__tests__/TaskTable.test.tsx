import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MantineProvider } from '@mantine/core';
import { TaskTable } from '../TaskTable';
import { trpc } from '../../utils/trpc';

// Mock tRPC
vi.mock('../../utils/trpc', () => ({
  trpc: {
    tasks: {
      getAll: {
        useQuery: vi.fn(),
      },
      create: {
        useMutation: vi.fn(),
      },
      delete: {
        useMutation: vi.fn(),
      },
    },
    fields: {
      getAll: {
        useQuery: vi.fn(),
      },
    },
  },
}));

// Mock notifications
vi.mock('@mantine/notifications', () => ({
  notifications: {
    show: vi.fn(),
  },
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <MantineProvider>
        {children}
      </MantineProvider>
    </QueryClientProvider>
  );
};

describe('TaskTable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders task table with empty state', () => {
    // Mock empty data
    (trpc.tasks.getAll.useQuery as any).mockReturnValue({
      data: [],
      isLoading: false,
      refetch: vi.fn(),
    });

    (trpc.fields.getAll.useQuery as any).mockReturnValue({
      data: [],
    });

    (trpc.tasks.create.useMutation as any).mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    });

    (trpc.tasks.delete.useMutation as any).mockReturnValue({
      mutate: vi.fn(),
    });

    render(
      <TestWrapper>
        <TaskTable />
      </TestWrapper>
    );

    expect(screen.getByText('Tasks')).toBeInTheDocument();
    expect(screen.getByText('Add Task')).toBeInTheDocument();
    expect(screen.getByText('No tasks found. Create your first task!')).toBeInTheDocument();
  });

  it('renders tasks when data is available', () => {
    const mockTasks = [
      {
        id: '1',
        name: 'Test Task',
        description: 'Test Description',
        status: 'Open',
        customFields: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    (trpc.tasks.getAll.useQuery as any).mockReturnValue({
      data: mockTasks,
      isLoading: false,
      refetch: vi.fn(),
    });

    (trpc.fields.getAll.useQuery as any).mockReturnValue({
      data: [],
    });

    (trpc.tasks.create.useMutation as any).mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    });

    (trpc.tasks.delete.useMutation as any).mockReturnValue({
      mutate: vi.fn(),
    });

    render(
      <TestWrapper>
        <TaskTable />
      </TestWrapper>
    );

    expect(screen.getByText('Test Task')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
    expect(screen.getByText('Open')).toBeInTheDocument();
  });

  it('opens create task modal when Add Task is clicked', () => {
    (trpc.tasks.getAll.useQuery as any).mockReturnValue({
      data: [],
      isLoading: false,
      refetch: vi.fn(),
    });

    (trpc.fields.getAll.useQuery as any).mockReturnValue({
      data: [],
    });

    (trpc.tasks.create.useMutation as any).mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    });

    (trpc.tasks.delete.useMutation as any).mockReturnValue({
      mutate: vi.fn(),
    });

    render(
      <TestWrapper>
        <TaskTable />
      </TestWrapper>
    );

    const addButton = screen.getByText('Add Task');
    fireEvent.click(addButton);

    expect(screen.getByText('Create New Task')).toBeInTheDocument();
    expect(screen.getByLabelText('Task Name')).toBeInTheDocument();
  });
});
