import { useState, useEffect } from 'react';
import { TextInput, NumberInput, Switch, Button, Group, Popover } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { trpc } from '../utils/trpc';
import type { EditingCell } from '../types';

interface InlineEditCellProps {
  taskId: string;
  fieldName: string;
  fieldType: 'string' | 'number' | 'boolean';
  currentValue: any;
  onClose: () => void;
  onSave: () => void;
}

export function InlineEditCell({
  taskId,
  fieldName,
  fieldType,
  currentValue,
  onClose,
  onSave,
}: InlineEditCellProps) {
  const [value, setValue] = useState(currentValue);
  const [opened, setOpened] = useState(true);

  const updateCustomField = trpc.tasks.updateCustomField.useMutation({
    onSuccess: () => {
      notifications.show({
        title: 'Success',
        message: 'Field updated successfully',
        color: 'green',
      });
      onSave();
      handleClose();
    },
    onError: (error) => {
      notifications.show({
        title: 'Error',
        message: error.message,
        color: 'red',
      });
    },
  });

  const updateTask = trpc.tasks.update.useMutation({
    onSuccess: () => {
      notifications.show({
        title: 'Success',
        message: 'Task updated successfully',
        color: 'green',
      });
      onSave();
      handleClose();
    },
    onError: (error) => {
      notifications.show({
        title: 'Error',
        message: error.message,
        color: 'red',
      });
    },
  });

  const handleClose = () => {
    setOpened(false);
    onClose();
  };

  const handleSave = () => {
    // Handle built-in fields vs custom fields
    if (['name', 'description', 'status'].includes(fieldName)) {
      updateTask.mutate({
        id: taskId,
        data: { [fieldName]: value },
      });
    } else {
      updateCustomField.mutate({
        taskId,
        fieldName,
        value,
      });
    }
  };

  const renderInput = () => {
    switch (fieldType) {
      case 'number':
        return (
          <NumberInput
            value={value}
            onChange={setValue}
            placeholder="Enter number"
            size="sm"
            autoFocus
          />
        );
      case 'boolean':
        return (
          <Switch
            checked={Boolean(value)}
            onChange={(e) => setValue(e.target.checked)}
            size="sm"
          />
        );
      default:
        return (
          <TextInput
            value={value || ''}
            onChange={(e) => setValue(e.target.value)}
            placeholder="Enter text"
            size="sm"
            autoFocus
          />
        );
    }
  };

  return (
    <Popover
      opened={opened}
      onClose={handleClose}
      position="bottom"
      withArrow
      shadow="md"
      trapFocus
    >
      <Popover.Target>
        <div />
      </Popover.Target>
      <Popover.Dropdown>
        <div style={{ minWidth: 200 }}>
          {renderInput()}
          <Group mt="sm" justify="flex-end">
            <Button
              size="xs"
              variant="light"
              onClick={handleClose}
            >
              Cancel
            </Button>
            <Button
              size="xs"
              onClick={handleSave}
              loading={updateCustomField.isPending || updateTask.isPending}
            >
              Save
            </Button>
          </Group>
        </div>
      </Popover.Dropdown>
    </Popover>
  );
}
