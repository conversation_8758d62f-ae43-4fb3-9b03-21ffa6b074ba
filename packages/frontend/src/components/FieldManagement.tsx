import { useState } from 'react';
import {
  Card,
  Title,
  Button,
  Group,
  TextInput,
  Select,
  Switch,
  Stack,
  Badge,
  ActionIcon,
  Text,
  Flex,
} from '@mantine/core';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { trpc } from '../utils/trpc';
import type { NewFieldDefinition } from '../types';

export function FieldManagement() {
  const [isAdding, setIsAdding] = useState(false);
  const [newField, setNewField] = useState<NewFieldDefinition>({
    name: '',
    label: '',
    type: 'string',
    required: 'false',
    defaultValue: null,
  });

  const { data: fields = [], refetch } = trpc.fields.getAll.useQuery();
  const createField = trpc.fields.create.useMutation({
    onSuccess: () => {
      notifications.show({
        title: 'Success',
        message: 'Field created successfully',
        color: 'green',
      });
      refetch();
      setIsAdding(false);
      setNewField({
        name: '',
        label: '',
        type: 'string',
        required: 'false',
        defaultValue: null,
      });
    },
    onError: (error) => {
      notifications.show({
        title: 'Error',
        message: error.message,
        color: 'red',
      });
    },
  });

  const deleteField = trpc.fields.delete.useMutation({
    onSuccess: () => {
      notifications.show({
        title: 'Success',
        message: 'Field deleted successfully',
        color: 'green',
      });
      refetch();
    },
    onError: (error) => {
      notifications.show({
        title: 'Error',
        message: error.message,
        color: 'red',
      });
    },
  });

  const handleSubmit = () => {
    if (!newField.name.trim() || !newField.label.trim()) {
      notifications.show({
        title: 'Validation Error',
        message: 'Name and label are required',
        color: 'red',
      });
      return;
    }

    createField.mutate(newField);
  };

  return (
    <Card withBorder>
      <Group justify="space-between" mb="md">
        <Title order={3}>Custom Fields</Title>
        <Button
          leftSection={<IconPlus size={16} />}
          onClick={() => setIsAdding(true)}
          disabled={isAdding}
        >
          Add Field
        </Button>
      </Group>

      {isAdding && (
        <Card withBorder mb="md" bg="gray.0">
          <Stack>
            <Group grow>
              <TextInput
                label="Field Name"
                placeholder="e.g., location"
                value={newField.name}
                onChange={(e) => setNewField({ ...newField, name: e.target.value })}
                description="Used internally (no spaces, alphanumeric + underscore)"
              />
              <TextInput
                label="Display Label"
                placeholder="e.g., Location"
                value={newField.label}
                onChange={(e) => setNewField({ ...newField, label: e.target.value })}
                description="Shown in the table header"
              />
            </Group>
            
            <Group grow>
              <Select
                label="Field Type"
                value={newField.type}
                onChange={(value) => setNewField({ ...newField, type: value as any })}
                data={[
                  { value: 'string', label: 'Text' },
                  { value: 'number', label: 'Number' },
                  { value: 'boolean', label: 'Yes/No' },
                ]}
              />
              <Switch
                label="Required Field"
                checked={newField.required === 'true'}
                onChange={(e) => setNewField({ ...newField, required: e.target.checked ? 'true' : 'false' })}
              />
            </Group>

            <Group>
              <Button onClick={handleSubmit} loading={createField.isPending}>
                Create Field
              </Button>
              <Button variant="light" onClick={() => setIsAdding(false)}>
                Cancel
              </Button>
            </Group>
          </Stack>
        </Card>
      )}

      <Flex wrap="wrap" gap="sm">
        {fields.map((field) => (
          <Badge
            key={field.id}
            size="lg"
            variant="light"
            rightSection={
              <ActionIcon
                size="xs"
                color="red"
                variant="transparent"
                onClick={() => deleteField.mutate({ id: field.id })}
              >
                <IconTrash size={12} />
              </ActionIcon>
            }
          >
            {field.label} ({field.type})
          </Badge>
        ))}
        {fields.length === 0 && !isAdding && (
          <Text c="dimmed">No custom fields defined</Text>
        )}
      </Flex>
    </Card>
  );
}
