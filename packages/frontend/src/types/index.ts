// Re-export types from backend
export type Task = {
  id: string;
  name: string;
  description: string | null;
  status: 'Open' | 'In Progress' | 'In Review' | 'Closed';
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
};

export type FieldDefinition = {
  id: string;
  name: string;
  label: string;
  type: 'string' | 'number' | 'boolean';
  required: 'true' | 'false';
  defaultValue: string | null;
  createdAt: Date;
  updatedAt: Date;
};

export type NewTask = Omit<Task, 'id' | 'createdAt' | 'updatedAt'>;
export type NewFieldDefinition = Omit<FieldDefinition, 'id' | 'createdAt' | 'updatedAt'>;

// UI-specific types
export type EditingCell = {
  taskId: string;
  fieldName: string;
  fieldType: 'string' | 'number' | 'boolean';
  currentValue: any;
} | null;
