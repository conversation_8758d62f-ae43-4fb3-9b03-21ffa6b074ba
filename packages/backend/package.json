{"name": "backend", "version": "1.0.0", "description": "Backend API for task management application", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit", "db:generate": "drizzle-kit generate:pg", "db:migrate": "tsx src/db/migrate.ts", "db:studio": "drizzle-kit studio"}, "dependencies": {"@trpc/server": "^10.45.0", "cors": "^2.8.5", "drizzle-orm": "^0.29.0", "drizzle-zod": "^0.5.1", "express": "^4.18.2", "pg": "^8.11.3", "zod": "^3.22.4", "dotenv": "^16.3.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/pg": "^8.10.9", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "drizzle-kit": "^0.20.4", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.0"}}