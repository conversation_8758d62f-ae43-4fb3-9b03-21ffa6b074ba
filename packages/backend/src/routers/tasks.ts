import { z } from 'zod';
import { router, protectedProcedure } from '../trpc/trpc';
import { tasks, insertTaskSchema, selectTaskSchema } from '../db/schema';
import { eq, desc } from 'drizzle-orm';
import { TRPCError } from '@trpc/server';

export const tasksRouter = router({
  // Get all tasks
  getAll: protectedProcedure.query(async ({ ctx }) => {
    try {
      const allTasks = await ctx.db
        .select()
        .from(tasks)
        .orderBy(desc(tasks.createdAt));
      
      return allTasks;
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch tasks',
        cause: error,
      });
    }
  }),

  // Get task by ID
  getById: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ ctx, input }) => {
      try {
        const task = await ctx.db
          .select()
          .from(tasks)
          .where(eq(tasks.id, input.id))
          .limit(1);

        if (task.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Task not found',
          });
        }

        return task[0];
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch task',
          cause: error,
        });
      }
    }),

  // Create new task
  create: protectedProcedure
    .input(insertTaskSchema.omit({ id: true, createdAt: true, updatedAt: true }))
    .mutation(async ({ ctx, input }) => {
      try {
        const newTask = await ctx.db
          .insert(tasks)
          .values({
            ...input,
            updatedAt: new Date(),
          })
          .returning();

        return newTask[0];
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create task',
          cause: error,
        });
      }
    }),

  // Update task
  update: protectedProcedure
    .input(
      z.object({
        id: z.string().uuid(),
        data: insertTaskSchema.omit({ id: true, createdAt: true, updatedAt: true }).partial(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const updatedTask = await ctx.db
          .update(tasks)
          .set({
            ...input.data,
            updatedAt: new Date(),
          })
          .where(eq(tasks.id, input.id))
          .returning();

        if (updatedTask.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Task not found',
          });
        }

        return updatedTask[0];
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update task',
          cause: error,
        });
      }
    }),

  // Delete task
  delete: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const deletedTask = await ctx.db
          .delete(tasks)
          .where(eq(tasks.id, input.id))
          .returning();

        if (deletedTask.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Task not found',
          });
        }

        return { success: true, deletedTask: deletedTask[0] };
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete task',
          cause: error,
        });
      }
    }),

  // Update custom field value for a task
  updateCustomField: protectedProcedure
    .input(
      z.object({
        taskId: z.string().uuid(),
        fieldName: z.string(),
        value: z.union([z.string(), z.number(), z.boolean()]),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // First get the current task to preserve existing custom fields
        const currentTask = await ctx.db
          .select()
          .from(tasks)
          .where(eq(tasks.id, input.taskId))
          .limit(1);

        if (currentTask.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Task not found',
          });
        }

        const existingCustomFields = currentTask[0].customFields || {};
        const updatedCustomFields = {
          ...existingCustomFields,
          [input.fieldName]: input.value,
        };

        const updatedTask = await ctx.db
          .update(tasks)
          .set({
            customFields: updatedCustomFields,
            updatedAt: new Date(),
          })
          .where(eq(tasks.id, input.taskId))
          .returning();

        return updatedTask[0];
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update custom field',
          cause: error,
        });
      }
    }),
});
