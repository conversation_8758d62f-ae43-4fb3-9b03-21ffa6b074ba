import { z } from 'zod';
import { router, protectedProcedure } from '../trpc/trpc';
import { fieldDefinitions, insertFieldDefinitionSchema } from '../db/schema';
import { eq, desc } from 'drizzle-orm';
import { TRPCError } from '@trpc/server';

export const fieldsRouter = router({
  // Get all field definitions
  getAll: protectedProcedure.query(async ({ ctx }) => {
    try {
      const allFields = await ctx.db
        .select()
        .from(fieldDefinitions)
        .orderBy(desc(fieldDefinitions.createdAt));
      
      return allFields;
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch field definitions',
        cause: error,
      });
    }
  }),

  // Get field definition by ID
  getById: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ ctx, input }) => {
      try {
        const field = await ctx.db
          .select()
          .from(fieldDefinitions)
          .where(eq(fieldDefinitions.id, input.id))
          .limit(1);

        if (field.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Field definition not found',
          });
        }

        return field[0];
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch field definition',
          cause: error,
        });
      }
    }),

  // Create new field definition
  create: protectedProcedure
    .input(insertFieldDefinitionSchema.omit({ id: true, createdAt: true, updatedAt: true }))
    .mutation(async ({ ctx, input }) => {
      try {
        const newField = await ctx.db
          .insert(fieldDefinitions)
          .values({
            ...input,
            updatedAt: new Date(),
          })
          .returning();

        return newField[0];
      } catch (error) {
        // Handle unique constraint violation
        if (error instanceof Error && error.message.includes('unique')) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'A field with this name already exists',
          });
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create field definition',
          cause: error,
        });
      }
    }),

  // Update field definition
  update: protectedProcedure
    .input(
      z.object({
        id: z.string().uuid(),
        data: insertFieldDefinitionSchema.omit({ id: true, createdAt: true, updatedAt: true }).partial(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const updatedField = await ctx.db
          .update(fieldDefinitions)
          .set({
            ...input.data,
            updatedAt: new Date(),
          })
          .where(eq(fieldDefinitions.id, input.id))
          .returning();

        if (updatedField.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Field definition not found',
          });
        }

        return updatedField[0];
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        // Handle unique constraint violation
        if (error instanceof Error && error.message.includes('unique')) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'A field with this name already exists',
          });
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update field definition',
          cause: error,
        });
      }
    }),

  // Delete field definition
  delete: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const deletedField = await ctx.db
          .delete(fieldDefinitions)
          .where(eq(fieldDefinitions.id, input.id))
          .returning();

        if (deletedField.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Field definition not found',
          });
        }

        return { success: true, deletedField: deletedField[0] };
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete field definition',
          cause: error,
        });
      }
    }),
});
