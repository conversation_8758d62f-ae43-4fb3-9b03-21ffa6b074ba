{"id": "f12fadd1-4c57-42ee-ba98-6a7a47c326a6", "prevId": "00000000-0000-0000-0000-000000000000", "version": "5", "dialect": "pg", "tables": {"field_definitions": {"name": "field_definitions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "field_type", "primaryKey": false, "notNull": true}, "required": {"name": "required", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "default": "'false'"}, "default_value": {"name": "default_value", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"field_definitions_name_unique": {"name": "field_definitions_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}}, "tasks": {"name": "tasks", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "task_status", "primaryKey": false, "notNull": true, "default": "'Open'"}, "custom_fields": {"name": "custom_fields", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {"field_type": {"name": "field_type", "values": {"string": "string", "number": "number", "boolean": "boolean"}}, "task_status": {"name": "task_status", "values": {"Open": "Open", "In Progress": "In Progress", "In Review": "In Review", "Closed": "Closed"}}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}