import { pgTable, uuid, varchar, text, timestamp, jsonb, pgEnum } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

// Enums
export const taskStatusEnum = pgEnum('task_status', ['Open', 'In Progress', 'In Review', 'Closed']);
export const fieldTypeEnum = pgEnum('field_type', ['string', 'number', 'boolean']);

// Tables
export const tasks = pgTable('tasks', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  status: taskStatusEnum('status').notNull().default('Open'),
  customFields: jsonb('custom_fields').$type<Record<string, any>>().default({}),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const fieldDefinitions = pgTable('field_definitions', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull().unique(),
  label: varchar('label', { length: 255 }).notNull(),
  type: fieldTypeEnum('type').notNull(),
  required: varchar('required', { length: 10 }).notNull().default('false'), // storing as string for consistency
  defaultValue: text('default_value'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Zod schemas for validation
export const insertTaskSchema = createInsertSchema(tasks, {
  name: z.string().min(1, 'Task name is required').max(255),
  description: z.string().optional(),
  status: z.enum(['Open', 'In Progress', 'In Review', 'Closed']),
  customFields: z.record(z.any()).optional(),
});

export const selectTaskSchema = createSelectSchema(tasks);

export const insertFieldDefinitionSchema = createInsertSchema(fieldDefinitions, {
  name: z.string().min(1, 'Field name is required').max(255).regex(/^[a-zA-Z_][a-zA-Z0-9_]*$/, 'Field name must be a valid identifier'),
  label: z.string().min(1, 'Field label is required').max(255),
  type: z.enum(['string', 'number', 'boolean']),
  required: z.enum(['true', 'false']).default('false'),
  defaultValue: z.string().optional(),
});

export const selectFieldDefinitionSchema = createSelectSchema(fieldDefinitions);

// Types
export type Task = typeof tasks.$inferSelect;
export type NewTask = typeof tasks.$inferInsert;
export type FieldDefinition = typeof fieldDefinitions.$inferSelect;
export type NewFieldDefinition = typeof fieldDefinitions.$inferInsert;

// Custom field value validation schema
export const customFieldValueSchema = z.object({
  fieldId: z.string().uuid(),
  value: z.union([z.string(), z.number(), z.boolean()]),
});
