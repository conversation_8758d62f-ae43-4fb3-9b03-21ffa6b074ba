import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { tasksRouter } from '../routers/tasks';
import { createContext } from '../trpc/context';
import { db } from '../db/connection';
import { tasks } from '../db/schema';

// Mock database for testing
const mockDb = {
  select: jest.fn().mockReturnThis(),
  from: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  values: jest.fn().mockReturnThis(),
  returning: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  set: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
};

// Mock context
const mockContext = {
  db: mockDb,
  req: {} as any,
  res: {} as any,
};

describe('Tasks Router', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getAll', () => {
    it('should return all tasks', async () => {
      const mockTasks = [
        {
          id: '1',
          name: 'Test Task',
          description: 'Test Description',
          status: 'Open',
          customFields: {},
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockDb.select.mockResolvedValue(mockTasks);

      const caller = tasksRouter.createCaller(mockContext);
      const result = await caller.getAll();

      expect(result).toEqual(mockTasks);
      expect(mockDb.select).toHaveBeenCalled();
      expect(mockDb.from).toHaveBeenCalledWith(tasks);
    });
  });

  describe('create', () => {
    it('should create a new task', async () => {
      const newTask = {
        name: 'New Task',
        description: 'New Description',
        status: 'Open' as const,
        customFields: {},
      };

      const createdTask = {
        id: '1',
        ...newTask,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDb.returning.mockResolvedValue([createdTask]);

      const caller = tasksRouter.createCaller(mockContext);
      const result = await caller.create(newTask);

      expect(result).toEqual(createdTask);
      expect(mockDb.insert).toHaveBeenCalledWith(tasks);
      expect(mockDb.values).toHaveBeenCalled();
      expect(mockDb.returning).toHaveBeenCalled();
    });

    it('should throw error for invalid input', async () => {
      const invalidTask = {
        name: '', // Empty name should fail validation
        description: 'Test',
        status: 'Open' as const,
        customFields: {},
      };

      const caller = tasksRouter.createCaller(mockContext);
      
      await expect(caller.create(invalidTask)).rejects.toThrow();
    });
  });
});
