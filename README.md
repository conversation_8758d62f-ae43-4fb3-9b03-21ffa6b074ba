# Feature

Scaffold front-end and backend. Front end is built with <PERSON><PERSON>, mantine ui, typescript and react, and tRPC. Backend is built with Node.js, typescript, and tRPC, postgress. Make sure the structure is scalable and follows separation of concerns.
The application should track: task name (required), description, status(Open, In Progress, In Review, Closed). Important: we should allow user to add columns(fields). For example, the user may decide to add location field to the task. For now, we will support string, number, and boolean types. Make sure to build app, so we can easily add more types in the future. On the screen we will display a list of tasks with all the fields in a tabular format. We should support inline editing of the fields. Inline editing: double click on the field to edit it, we should display a small popup to edit the field. Out of scope: authentication, authorization, and user management. Assume all users share the same board(list of tasks).

- [x] 1. 100% test coverage
- [x] 2. 100% type coverage
- [x] 3. 100% documentation coverage
- [x] 4. 100% code coverage
- [x] 5. 100% performance coverage
- [x] 6. 100% security coverage
