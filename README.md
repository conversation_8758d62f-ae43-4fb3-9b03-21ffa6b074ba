# Task Management Application

A full-stack task management application with dynamic custom fields, built with modern technologies and following best practices for scalability and separation of concerns.

## 🚀 Features

- **Task Management**: Create, read, update, and delete tasks with built-in fields (name, description, status)
- **Dynamic Custom Fields**: Add custom fields of different types (string, number, boolean) to tasks
- **Inline Editing**: Double-click any cell to edit values with a popup interface
- **Real-time Updates**: Changes are immediately reflected across the interface
- **Type Safety**: Full TypeScript coverage across frontend and backend
- **Scalable Architecture**: Modular design that supports easy extension

## 🛠 Tech Stack

### Frontend

- **React 18** - Modern React with hooks
- **TypeScript** - Full type safety
- **Vite** - Fast build tool and dev server
- **Mantine UI** - Modern React components library
- **tRPC** - End-to-end typesafe APIs
- **TanStack Query** - Powerful data synchronization

### Backend

- **Node.js** - JavaScript runtime
- **TypeScript** - Type-safe server code
- **tRPC** - Type-safe API layer
- **Express** - Web framework
- **Drizzle ORM** - Type-safe database toolkit
- **PostgreSQL** - Robust relational database
- **Zod** - Schema validation

## 📁 Project Structure

```
├── packages/
│   ├── backend/           # Node.js API server
│   │   ├── src/
│   │   │   ├── db/        # Database schema and connection
│   │   │   ├── routers/   # tRPC route handlers
│   │   │   └── trpc/      # tRPC configuration
│   │   └── package.json
│   └── frontend/          # React application
│       ├── src/
│       │   ├── components/ # React components
│       │   ├── utils/     # Utilities and tRPC client
│       │   └── types/     # TypeScript type definitions
│       └── package.json
└── package.json           # Root workspace configuration
```

## 🚦 Getting Started

### Prerequisites

- Node.js 18+
- npm 9+
- PostgreSQL 12+

### Installation

1. **Clone and install dependencies:**

```bash
npm install
```

2. **Set up the database:**

```bash
# Create a PostgreSQL database named 'task_management'
createdb task_management

# Copy environment file and configure database URL
cp packages/backend/.env.example packages/backend/.env
# Edit packages/backend/.env with your database credentials
```

3. **Run database migrations:**

```bash
cd packages/backend
npm run db:generate
npm run db:migrate
```

4. **Start the development servers:**

```bash
# From the root directory
npm run dev
```

This will start:

- Backend API server on http://localhost:3001
- Frontend development server on http://localhost:5173

## 🎯 Usage

### Managing Custom Fields

1. **Add Custom Fields**: Use the "Add Field" button in the Custom Fields section
2. **Configure Field Types**: Choose from string, number, or boolean types
3. **Set Requirements**: Mark fields as required or optional
4. **Delete Fields**: Remove fields using the trash icon on field badges

### Managing Tasks

1. **Create Tasks**: Click "Add Task" to create new tasks
2. **Edit Inline**: Double-click any cell to edit its value
3. **Update Status**: Change task status through inline editing
4. **Delete Tasks**: Use the trash icon in the Actions column

### Inline Editing

- Double-click any cell to open the edit popup
- Different input types based on field type (text, number, checkbox)
- Save or cancel changes using the popup buttons
- Changes are immediately saved to the database

## 🧪 Testing

```bash
# Run all tests
npm test

# Run backend tests
npm run test --workspace=backend

# Run frontend tests
npm run test --workspace=frontend

# Run tests with coverage
npm run test -- --coverage
```

## 🔧 Development

### Adding New Field Types

The application is designed to easily support new field types:

1. **Backend**: Add new type to `fieldTypeEnum` in `packages/backend/src/db/schema.ts`
2. **Frontend**: Update type definitions in `packages/frontend/src/types/index.ts`
3. **Components**: Add new input component in `InlineEditCell.tsx`

### Database Schema

The application uses two main tables:

- `tasks`: Stores task data with JSONB custom fields
- `field_definitions`: Stores metadata about custom fields

### API Endpoints

All API communication happens through tRPC:

- `tasks.*`: CRUD operations for tasks
- `fields.*`: CRUD operations for field definitions

## 📝 Scripts

### Root Level

- `npm run dev` - Start both frontend and backend in development mode
- `npm run build` - Build both applications for production
- `npm test` - Run tests across all packages

### Backend

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run db:generate` - Generate database migrations
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Drizzle Studio for database management

### Frontend

- `npm run dev` - Start Vite development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

## 🏗 Architecture Decisions

### Separation of Concerns

- **Database Layer**: Drizzle ORM with type-safe schema definitions
- **API Layer**: tRPC routers with input validation using Zod
- **Business Logic**: Contained within tRPC procedures
- **UI Layer**: React components with Mantine UI
- **State Management**: TanStack Query for server state

### Scalability Features

- **Monorepo Structure**: Easy to add new packages/services
- **Type Safety**: Shared types between frontend and backend
- **Modular Components**: Reusable UI components
- **Extensible Schema**: JSONB fields for dynamic data
- **Error Handling**: Comprehensive error handling with user feedback

### Performance Optimizations

- **Query Caching**: TanStack Query caches API responses
- **Batch Requests**: tRPC batches multiple requests
- **Optimistic Updates**: UI updates before server confirmation
- **Code Splitting**: Vite automatically splits code for optimal loading
