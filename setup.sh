#!/bin/bash

# Task Management Application Setup Script

set -e

echo "🚀 Setting up Task Management Application..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL is not installed. Please install PostgreSQL 12+ and try again."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Set up environment file
echo "⚙️ Setting up environment configuration..."
if [ ! -f packages/backend/.env ]; then
    cp packages/backend/.env.example packages/backend/.env
    echo "✅ Created packages/backend/.env from example"
    echo "📝 Please edit packages/backend/.env with your database credentials"
else
    echo "✅ Environment file already exists"
fi

# Create database if it doesn't exist
echo "🗄️ Setting up database..."
if ! psql -lqt | cut -d \| -f 1 | grep -qw task_management; then
    createdb task_management
    echo "✅ Created database 'task_management'"
else
    echo "✅ Database 'task_management' already exists"
fi

# Generate and run migrations
echo "🔄 Running database migrations..."
cd packages/backend
npm run db:generate
npm run db:migrate
cd ../..

echo "✅ Setup complete!"
echo ""
echo "🎯 Next steps:"
echo "1. Edit packages/backend/.env with your database credentials if needed"
echo "2. Run 'npm run dev' to start the development servers"
echo "3. Open http://localhost:5173 in your browser"
echo ""
echo "📚 Available commands:"
echo "  npm run dev          - Start both frontend and backend"
echo "  npm run build        - Build for production"
echo "  npm test             - Run tests"
echo "  npm run lint         - Run linting"
echo ""
echo "Happy coding! 🎉"
