{"name": "task-management-app", "version": "1.0.0", "description": "Full-stack task management application with dynamic fields", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run dev --workspace=backend", "dev:frontend": "npm run dev --workspace=frontend", "build": "npm run build --workspace=backend && npm run build --workspace=frontend", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "type-check": "npm run type-check --workspaces"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}