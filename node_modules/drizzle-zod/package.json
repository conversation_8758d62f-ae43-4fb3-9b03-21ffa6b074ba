{"name": "drizzle-zod", "version": "0.5.1", "description": "Generate Zod schemas from Drizzle ORM schemas", "type": "module", "scripts": {"build": "tsx scripts/build.ts", "b": "pnpm build", "test:types": "cd tests && tsc", "pack": "(cd dist && npm pack --pack-destination ..) && rm -f package.tgz && mv *.tgz package.tgz", "publish": "npm publish package.tgz", "test": "ava tests"}, "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.cjs", "default": "./index.cjs"}, "types": "./index.d.ts", "default": "./index.mjs"}}, "main": "./index.cjs", "module": "./index.mjs", "types": "./index.d.ts", "publishConfig": {"provenance": true}, "repository": {"type": "git", "url": "git+https://github.com/drizzle-team/drizzle-orm.git"}, "ava": {"files": ["tests/**/*.test.ts", "!tests/bun/**/*"], "extensions": {"ts": "module"}, "nodeArguments": ["--loader=tsx"]}, "keywords": ["zod", "validate", "validation", "schema", "drizzle", "orm", "pg", "mysql", "postgresql", "postgres", "sqlite", "database", "sql", "typescript", "ts"], "author": "Drizzle Team", "license": "Apache-2.0", "peerDependencies": {"drizzle-orm": ">=0.23.13", "zod": "*"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.1", "@rollup/plugin-typescript": "^11.1.0", "@types/node": "^18.15.10", "ava": "^5.1.0", "cpy": "^10.1.0", "drizzle-orm": "link:../drizzle-orm/dist", "rimraf": "^5.0.0", "rollup": "^3.20.7", "tsx": "^3.12.2", "zod": "^3.20.2", "zx": "^7.2.2"}}