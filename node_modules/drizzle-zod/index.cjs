"use strict";var e=require("drizzle-orm"),t=require("drizzle-orm/mysql-core"),r=require("drizzle-orm/pg-core"),n=require("drizzle-orm/sqlite-core"),a=require("zod");const i=a.z.union([a.z.string(),a.z.number(),a.z.boolean(),a.z.null()]),s=a.z.lazy((()=>a.z.union([i,a.z.array(s),a.z.record(s)])));function o(i){let l;if(function(e){return"enumValues"in e&&Array.isArray(e.enumValues)&&e.enumValues.length>0}(i)&&(l=i.enumValues.length?a.z.enum(i.enumValues):a.z.string()),!l)if(e.is(i,r.PgUUID))l=a.z.string().uuid();else if("custom"===i.dataType)l=a.z.any();else if("json"===i.dataType)l=s;else if("array"===i.dataType)l=a.z.array(o(i.baseColumn));else if("number"===i.dataType)l=a.z.number();else if("bigint"===i.dataType)l=a.z.bigint();else if("boolean"===i.dataType)l=a.z.boolean();else if("date"===i.dataType)l=a.z.date();else if("string"===i.dataType){let s=a.z.string();(e.is(i,r.PgChar)||e.is(i,r.PgVarchar)||e.is(i,t.MySqlVarChar)||e.is(i,t.MySqlVarBinary)||e.is(i,t.MySqlChar)||e.is(i,n.SQLiteText))&&"number"==typeof i.length&&(s=s.max(i.length)),l=s}return l||(l=a.z.any()),l}exports.createInsertSchema=function(t,r){const n=e.getTableColumns(t),i=Object.entries(n);let s=Object.fromEntries(i.map((([e,t])=>[e,o(t)])));r&&(s=Object.assign(s,Object.fromEntries(Object.entries(r).map((([e,t])=>[e,"function"==typeof t?t(s):t])))));for(const[e,t]of i)t.notNull?t.hasDefault&&(s[e]=s[e].optional()):s[e]=s[e].nullable().optional();return a.z.object(s)},exports.createSelectSchema=function(t,r){const n=e.getTableColumns(t),i=Object.entries(n);let s=Object.fromEntries(i.map((([e,t])=>[e,o(t)])));r&&(s=Object.assign(s,Object.fromEntries(Object.entries(r).map((([e,t])=>[e,"function"==typeof t?t(s):t])))));for(const[e,t]of i)t.notNull||(s[e]=s[e].nullable());return a.z.object(s)},exports.jsonSchema=s;
//# sourceMappingURL=index.cjs.map
