{"version": 3, "file": "index.mjs", "sources": ["../src/index.ts"], "sourcesContent": [null], "names": ["literalSchema", "z", "union", "string", "number", "boolean", "null", "jsonSchema", "lazy", "array", "record", "createInsertSchema", "table", "refine", "columns", "getTableColumns", "columnEntries", "Object", "entries", "schemaEntries", "fromEntries", "map", "name", "column", "mapColumnToSchema", "assign", "refineColumn", "notNull", "<PERSON><PERSON><PERSON><PERSON>", "optional", "nullable", "object", "createSelectSchema", "type", "Array", "isArray", "enum<PERSON><PERSON><PERSON>", "length", "isWithEnum", "enum", "is", "PgUUID", "uuid", "dataType", "any", "baseColumn", "bigint", "date", "sType", "PgChar", "PgVarchar", "MySqlVarChar", "MySqlVarBinary", "MySqlChar", "SQLiteText", "max"], "mappings": "oSAeA,MAAMA,EAAgBC,EAAEC,MAAM,CAACD,EAAEE,SAAUF,EAAEG,SAAUH,EAAEI,UAAWJ,EAAEK,SAGzDC,EAA8BN,EAAEO,MAAK,IACjDP,EAAEC,MAAM,CAACF,EAAeC,EAAEQ,MAAMF,GAAaN,EAAES,OAAOH,OA+EjD,SAAUI,EAIfC,EAIAC,GAKA,MAAMC,EAAUC,EAAgBH,GAC1BI,EAAgBC,OAAOC,QAAQJ,GAErC,IAAIK,EAAgBF,OAAOG,YAAYJ,EAAcK,KAAI,EAAEC,EAAMC,KACzD,CAACD,EAAME,EAAkBD,OAG7BV,IACHM,EAAgBF,OAAOQ,OACtBN,EACAF,OAAOG,YACNH,OAAOC,QAAQL,GAAQQ,KAAI,EAAEC,EAAMI,KAC3B,CACNJ,EACwB,mBAAjBI,EACJA,EAAaP,GACbO,QAOR,IAAK,MAAOJ,EAAMC,KAAWP,EACvBO,EAAOI,QAEDJ,EAAOK,aACjBT,EAAcG,GAAQH,EAAcG,GAAOO,YAF3CV,EAAcG,GAAQH,EAAcG,GAAOQ,WAAWD,WAMxD,OAAO5B,EAAE8B,OAAOZ,EACjB,CAEM,SAAUa,EAIfpB,EAIAC,GAKA,MAAMC,EAAUC,EAAgBH,GAC1BI,EAAgBC,OAAOC,QAAQJ,GAErC,IAAIK,EAAgBF,OAAOG,YAAYJ,EAAcK,KAAI,EAAEC,EAAMC,KACzD,CAACD,EAAME,EAAkBD,OAG7BV,IACHM,EAAgBF,OAAOQ,OACtBN,EACAF,OAAOG,YACNH,OAAOC,QAAQL,GAAQQ,KAAI,EAAEC,EAAMI,KAC3B,CACNJ,EACwB,mBAAjBI,EACJA,EAAaP,GACbO,QAOR,IAAK,MAAOJ,EAAMC,KAAWP,EACvBO,EAAOI,UACXR,EAAcG,GAAQH,EAAcG,GAAOQ,YAI7C,OAAO7B,EAAE8B,OAAOZ,EACjB,CAMA,SAASK,EAAkBD,GAC1B,IAAIU,EAMJ,GAXD,SAAoBV,GACnB,MAAO,eAAgBA,GAAUW,MAAMC,QAAQZ,EAAOa,aAAeb,EAAOa,WAAWC,OAAS,CACjG,CAKKC,CAAWf,KACdU,EAAOV,EAAOa,WAAWC,OAASpC,EAAEsC,KAAKhB,EAAOa,YAAcnC,EAAEE,WAG5D8B,EACJ,GAAIO,EAAGjB,EAAQkB,GACdR,EAAOhC,EAAEE,SAASuC,YACZ,GAAwB,WAApBnB,EAAOoB,SACjBV,EAAOhC,EAAE2C,WACH,GAAwB,SAApBrB,EAAOoB,SACjBV,EAAO1B,OACD,GAAwB,UAApBgB,EAAOoB,SACjBV,EAAOhC,EAAEQ,MAAMe,EAAmBD,EAA6BsB,kBACzD,GAAwB,WAApBtB,EAAOoB,SACjBV,EAAOhC,EAAEG,cACH,GAAwB,WAApBmB,EAAOoB,SACjBV,EAAOhC,EAAE6C,cACH,GAAwB,YAApBvB,EAAOoB,SACjBV,EAAOhC,EAAEI,eACH,GAAwB,SAApBkB,EAAOoB,SACjBV,EAAOhC,EAAE8C,YACH,GAAwB,WAApBxB,EAAOoB,SAAuB,CACxC,IAAIK,EAAQ/C,EAAEE,UAGZqC,EAAGjB,EAAQ0B,IAAWT,EAAGjB,EAAQ2B,IAAcV,EAAGjB,EAAQ4B,IACvDX,EAAGjB,EAAQ6B,IAAmBZ,EAAGjB,EAAQ8B,IAAcb,EAAGjB,EAAQ+B,KACzC,iBAAlB/B,EAAOc,SAElBW,EAAQA,EAAMO,IAAIhC,EAAOc,SAG1BJ,EAAOe,CACP,CAOF,OAJKf,IACJA,EAAOhC,EAAE2C,OAGHX,CACR"}