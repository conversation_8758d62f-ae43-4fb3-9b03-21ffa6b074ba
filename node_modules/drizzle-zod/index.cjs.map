{"version": 3, "file": "index.cjs", "sources": ["../src/index.ts"], "sourcesContent": [null], "names": ["literalSchema", "z", "union", "string", "number", "boolean", "null", "jsonSchema", "lazy", "array", "record", "mapColumnToSchema", "column", "type", "Array", "isArray", "enum<PERSON><PERSON><PERSON>", "length", "isWithEnum", "enum", "is", "PgUUID", "uuid", "dataType", "any", "baseColumn", "bigint", "date", "sType", "PgChar", "PgVarchar", "MySqlVarChar", "MySqlVarBinary", "MySqlChar", "SQLiteText", "max", "table", "refine", "columns", "getTableColumns", "columnEntries", "Object", "entries", "schemaEntries", "fromEntries", "map", "name", "assign", "refineColumn", "notNull", "<PERSON><PERSON><PERSON><PERSON>", "optional", "nullable", "object"], "mappings": "qKAeA,MAAMA,EAAgBC,EAACA,EAACC,MAAM,CAACD,EAAAA,EAAEE,SAAUF,EAACA,EAACG,SAAUH,EAAAA,EAAEI,UAAWJ,EAACA,EAACK,SAGzDC,EAA8BN,EAACA,EAACO,MAAK,IACjDP,EAAAA,EAAEC,MAAM,CAACF,EAAeC,EAACA,EAACQ,MAAMF,GAAaN,EAAAA,EAAES,OAAOH,OA+KvD,SAASI,EAAkBC,GAC1B,IAAIC,EAMJ,GAXD,SAAoBD,GACnB,MAAO,eAAgBA,GAAUE,MAAMC,QAAQH,EAAOI,aAAeJ,EAAOI,WAAWC,OAAS,CACjG,CAKKC,CAAWN,KACdC,EAAOD,EAAOI,WAAWC,OAAShB,EAACA,EAACkB,KAAKP,EAAOI,YAAcf,EAACA,EAACE,WAG5DU,EACJ,GAAIO,EAAEA,GAACR,EAAQS,EAAAA,QACdR,EAAOZ,EAACA,EAACE,SAASmB,YACZ,GAAwB,WAApBV,EAAOW,SACjBV,EAAOZ,EAAAA,EAAEuB,WACH,GAAwB,SAApBZ,EAAOW,SACjBV,EAAON,OACD,GAAwB,UAApBK,EAAOW,SACjBV,EAAOZ,EAACA,EAACQ,MAAME,EAAmBC,EAA6Ba,kBACzD,GAAwB,WAApBb,EAAOW,SACjBV,EAAOZ,EAAAA,EAAEG,cACH,GAAwB,WAApBQ,EAAOW,SACjBV,EAAOZ,EAAAA,EAAEyB,cACH,GAAwB,YAApBd,EAAOW,SACjBV,EAAOZ,EAAAA,EAAEI,eACH,GAAwB,SAApBO,EAAOW,SACjBV,EAAOZ,EAAAA,EAAE0B,YACH,GAAwB,WAApBf,EAAOW,SAAuB,CACxC,IAAIK,EAAQ3B,IAAEE,UAGZiB,EAAEA,GAACR,EAAQiB,EAAAA,SAAWT,EAAAA,GAAGR,EAAQkB,cAAcV,KAAGR,EAAQmB,EAAAA,eACvDX,KAAGR,EAAQoB,EAAcA,iBAAKZ,EAAEA,GAACR,EAAQqB,EAAAA,YAAcb,EAAAA,GAAGR,EAAQsB,gBACzC,iBAAlBtB,EAAOK,SAElBW,EAAQA,EAAMO,IAAIvB,EAAOK,SAG1BJ,EAAOe,CACP,CAOF,OAJKf,IACJA,EAAOZ,EAAAA,EAAEuB,OAGHX,CACR,4BA5IM,SAILuB,EAIAC,GAKA,MAAMC,EAAUC,kBAAgBH,GAC1BI,EAAgBC,OAAOC,QAAQJ,GAErC,IAAIK,EAAgBF,OAAOG,YAAYJ,EAAcK,KAAI,EAAEC,EAAMlC,KACzD,CAACkC,EAAMnC,EAAkBC,OAG7ByB,IACHM,EAAgBF,OAAOM,OACtBJ,EACAF,OAAOG,YACNH,OAAOC,QAAQL,GAAQQ,KAAI,EAAEC,EAAME,KAC3B,CACNF,EACwB,mBAAjBE,EACJA,EAAaL,GACbK,QAOR,IAAK,MAAOF,EAAMlC,KAAW4B,EACvB5B,EAAOqC,QAEDrC,EAAOsC,aACjBP,EAAcG,GAAQH,EAAcG,GAAOK,YAF3CR,EAAcG,GAAQH,EAAcG,GAAOM,WAAWD,WAMxD,OAAOlD,EAACA,EAACoD,OAAOV,EACjB,6BAEM,SAILP,EAIAC,GAKA,MAAMC,EAAUC,kBAAgBH,GAC1BI,EAAgBC,OAAOC,QAAQJ,GAErC,IAAIK,EAAgBF,OAAOG,YAAYJ,EAAcK,KAAI,EAAEC,EAAMlC,KACzD,CAACkC,EAAMnC,EAAkBC,OAG7ByB,IACHM,EAAgBF,OAAOM,OACtBJ,EACAF,OAAOG,YACNH,OAAOC,QAAQL,GAAQQ,KAAI,EAAEC,EAAME,KAC3B,CACNF,EACwB,mBAAjBE,EACJA,EAAaL,GACbK,QAOR,IAAK,MAAOF,EAAMlC,KAAW4B,EACvB5B,EAAOqC,UACXN,EAAcG,GAAQH,EAAcG,GAAOM,YAI7C,OAAOnD,EAACA,EAACoD,OAAOV,EACjB"}