import{getTableColumns as e,is as t}from"drizzle-orm";import{MySqlVarChar as n,MySqlVarBinary as r,MySqlChar as o}from"drizzle-orm/mysql-core";import{PgUUID as i,PgChar as a,PgVarchar as l}from"drizzle-orm/pg-core";import{SQLiteText as s}from"drizzle-orm/sqlite-core";import{z as u}from"zod";const m=u.union([u.string(),u.number(),u.boolean(),u.null()]),f=u.lazy((()=>u.union([m,u.array(f),u.record(f)])));function c(t,n){const r=e(t),o=Object.entries(r);let i=Object.fromEntries(o.map((([e,t])=>[e,p(t)])));n&&(i=Object.assign(i,Object.fromEntries(Object.entries(n).map((([e,t])=>[e,"function"==typeof t?t(i):t])))));for(const[e,t]of o)t.notNull?t.hasDefault&&(i[e]=i[e].optional()):i[e]=i[e].nullable().optional();return u.object(i)}function b(t,n){const r=e(t),o=Object.entries(r);let i=Object.fromEntries(o.map((([e,t])=>[e,p(t)])));n&&(i=Object.assign(i,Object.fromEntries(Object.entries(n).map((([e,t])=>[e,"function"==typeof t?t(i):t])))));for(const[e,t]of o)t.notNull||(i[e]=i[e].nullable());return u.object(i)}function p(e){let m;if(function(e){return"enumValues"in e&&Array.isArray(e.enumValues)&&e.enumValues.length>0}(e)&&(m=e.enumValues.length?u.enum(e.enumValues):u.string()),!m)if(t(e,i))m=u.string().uuid();else if("custom"===e.dataType)m=u.any();else if("json"===e.dataType)m=f;else if("array"===e.dataType)m=u.array(p(e.baseColumn));else if("number"===e.dataType)m=u.number();else if("bigint"===e.dataType)m=u.bigint();else if("boolean"===e.dataType)m=u.boolean();else if("date"===e.dataType)m=u.date();else if("string"===e.dataType){let i=u.string();(t(e,a)||t(e,l)||t(e,n)||t(e,r)||t(e,o)||t(e,s))&&"number"==typeof e.length&&(i=i.max(e.length)),m=i}return m||(m=u.any()),m}export{c as createInsertSchema,b as createSelectSchema,f as jsonSchema};
//# sourceMappingURL=index.mjs.map
